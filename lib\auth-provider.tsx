"use client"

import type React from "react"

import { createContext, useContext, useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { SettingsStorage, Role } from "@/lib/settings-storage"
import { locationCache } from "@/lib/location-cache"
import { PERMISSIONS, ROLE_PERMISSIONS } from "@/lib/permissions"

interface User {
  id: string
  name: string
  email: string
  role: "super_admin" | "org_admin" | "location_manager" | "staff" | "receptionist"
  locations: string[] // "all" or location IDs like "loc1", "loc2", etc.
}

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  currentLocation: string
  setCurrentLocation: (location: string) => void
  login: (user: User) => Promise<void>
  logout: () => void
  hasPermission: (permission: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  getUserPermissions: () => string[]
  canAccessLocation: (locationId: string) => boolean
  canAccessStaffData: (staffId: string) => boolean
  canAccessClientData: (clientId: string) => boolean
  getUserAccessibleLocations: () => string[]
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  currentLocation: "all",
  setCurrentLocation: () => {},
  login: async () => {},
  logout: () => {},
  hasPermission: () => false,
  hasAnyPermission: () => false,
  getUserPermissions: () => [],
  canAccessLocation: () => false,
  canAccessStaffData: () => false,
  canAccessClientData: () => false,
  getUserAccessibleLocations: () => [],
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [currentLocation, setCurrentLocation] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  // Check for stored user and location on mount
  useEffect(() => {
    console.log("🔍 Auth Provider - Initializing from localStorage...")

    // Load user data
    const storedUser = localStorage.getItem("vanity_user")
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser)
        console.log("🔍 Auth Provider - Loaded user from localStorage:", userData)
        setUser(userData)

        // For location-restricted users, immediately enforce their location
        if (!userData.locations.includes("all") && userData.locations.length > 0) {
          const assignedLocation = userData.locations[0]
          console.log("🔍 Auth Provider - Location-restricted user detected, enforcing location:", assignedLocation)
          setCurrentLocation(assignedLocation)
          localStorage.setItem("vanity_location", assignedLocation)
        }
      } catch (error) {
        console.error("Failed to parse stored user:", error)
        localStorage.removeItem("vanity_user")
      }
    }

    // Load location data (but don't override location-restricted users)
    const storedLocation = localStorage.getItem("vanity_location")
    if (storedLocation && (!storedUser || JSON.parse(storedUser).locations.includes("all"))) {
      console.log("🔍 Auth Provider - Loading location from localStorage:", storedLocation)
      setCurrentLocation(storedLocation)
    }

    setIsLoading(false)
  }, [])

  // Update location when user changes and validate against available locations
  useEffect(() => {
    if (user) {
      // Add a small delay to ensure location cache is initialized
      const validateLocation = () => {
        // Get available locations from database cache instead of localStorage
        const availableLocations = locationCache.getAllLocations()
        const availableLocationIds = availableLocations.map(loc => loc.id)

        // Add special locations
        availableLocationIds.push("all")
        availableLocationIds.push("home")
        availableLocationIds.push("online")

        console.log("🔍 Auth Provider - Available location IDs:", availableLocationIds)
        console.log("🔍 Auth Provider - Current location:", currentLocation)
        console.log("🔍 Auth Provider - User locations:", user.locations)

        // Only validate if we have locations loaded (avoid validation during initial load)
        if (availableLocationIds.length <= 3) { // Only special locations, no database locations yet
          console.log("🔍 Auth Provider - Waiting for locations to load...")
          return
        }

        // For location-restricted users, automatically set their location and prevent changes
        if (!user.locations.includes("all") && user.locations.length > 0) {
          // Find the first location that exists in both user's allowed locations and available locations
          const firstValidLocation = user.locations.find(loc => availableLocationIds.includes(loc))
          if (firstValidLocation) {
            console.log("🔍 Auth Provider - Location-restricted user, setting to assigned location:", firstValidLocation)
            console.log("🔍 Auth Provider - User can only access:", user.locations)

            // ALWAYS force set to their assigned location for location-restricted users
            if (currentLocation !== firstValidLocation) {
              console.log("🔍 Auth Provider - Forcing location change from", currentLocation, "to", firstValidLocation)
              setCurrentLocation(firstValidLocation)
              localStorage.setItem("vanity_location", firstValidLocation)
            }

            // Also check if current location is not in their allowed locations
            if (!user.locations.includes(currentLocation)) {
              console.log("🚫 Auth Provider - Current location not allowed for user, forcing change to:", firstValidLocation)
              setCurrentLocation(firstValidLocation)
              localStorage.setItem("vanity_location", firstValidLocation)
            }
          } else {
            console.log("🔍 Auth Provider - No valid location found for restricted user, this is an error")
            console.log("🔍 Auth Provider - User locations:", user.locations)
            console.log("🔍 Auth Provider - Available locations:", availableLocationIds)
          }
        } else if (user.locations.includes("all")) {
          // Admin users can access all locations
          if (currentLocation !== "all" && !availableLocationIds.includes(currentLocation)) {
            // If current location no longer exists, reset to "all"
            console.log("🔍 Auth Provider - Admin user's current location not available, resetting to 'all'")
            setCurrentLocation("all")
          } else {
            console.log("🔍 Auth Provider - Admin user location validation passed:", currentLocation)
          }
        }
      }

      // Run validation immediately and then after a short delay to catch late-loading locations
      validateLocation()
      const timeoutId = setTimeout(validateLocation, 500)

      return () => clearTimeout(timeoutId)
    }
  }, [user, currentLocation])

  const login = async (userData: User): Promise<void> => {
    return new Promise((resolve) => {
      // Simulate API call delay
      setTimeout(() => {
        setUser(userData)
        localStorage.setItem("vanity_user", JSON.stringify(userData))

        // For location-restricted users, immediately set their location
        if (!userData.locations.includes("all") && userData.locations.length > 0) {
          const assignedLocation = userData.locations[0]
          console.log("🔍 Login - Setting location-restricted user to:", assignedLocation)
          setCurrentLocation(assignedLocation)
          localStorage.setItem("vanity_location", assignedLocation)
        }

        toast({
          title: "Logged in successfully",
          description: `Welcome back, ${userData.name}!`,
        })

        resolve()
      }, 1000)
    })
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem("vanity_user")
    localStorage.removeItem("vanity_location") // Clear location data
    setCurrentLocation("all")

    toast({
      title: "Logged out",
      description: "You have been logged out successfully.",
    })
  }

  // Get user permissions based on role
  const getUserPermissions = (): string[] => {
    if (!user) return []

    // Get role permissions from our constants
    const roleKey = user.role.toUpperCase() as keyof typeof ROLE_PERMISSIONS
    const permissions = ROLE_PERMISSIONS[roleKey] || []

    // Get custom permissions from settings if they exist
    const storedRoles = SettingsStorage.getRoles()
    const userRole = storedRoles.find(role => role.id === user.role)

    if (userRole && userRole.permissions) {
      // If the role has custom permissions defined in settings, use those
      return userRole.permissions
    }

    // Otherwise use the default permissions for the role
    return permissions
  }

  // Check if user has a specific permission
  const hasPermission = (permission: string): boolean => {
    if (!user) return false

    // Special case for Receptionist role and POS access
    if (user.role === 'receptionist' && permission === PERMISSIONS.VIEW_POS) {
      return true
    }

    const permissions = getUserPermissions()

    // If user has ALL permission, they have access to everything
    if (permissions.includes(PERMISSIONS.ALL)) {
      return true
    }

    // Check if the user has the specific permission
    return permissions.includes(permission)
  }

  // Check if user has any of the specified permissions
  const hasAnyPermission = (permissionList: string[]): boolean => {
    if (!user) return false

    // Special case for Receptionist role and POS access
    if (user.role === 'receptionist' && permissionList.includes(PERMISSIONS.VIEW_POS)) {
      return true
    }

    const permissions = getUserPermissions()

    // If user has ALL permission, they have access to everything
    if (permissions.includes(PERMISSIONS.ALL)) {
      return true
    }

    // Log permissions for debugging
    if (permissionList.includes(PERMISSIONS.VIEW_POS)) {
      console.log("Checking POS permissions:")
      console.log("User role:", user.role)
      console.log("User permissions:", permissions)
      console.log("Required permissions:", permissionList)
      console.log("Has VIEW_POS:", permissions.includes(PERMISSIONS.VIEW_POS))
    }

    // Check if the user has any of the permissions
    return permissionList.some(permission => permissions.includes(permission))
  }

  // Check if user can access a specific location
  const canAccessLocation = (locationId: string): boolean => {
    if (!user) {
      console.log("🔍 canAccessLocation - No user logged in")
      return false
    }

    console.log(`🔍 canAccessLocation - Checking access for location: ${locationId}`)
    console.log(`🔍 canAccessLocation - User role: ${user.role}`)
    console.log(`🔍 canAccessLocation - User locations: ${user.locations.join(', ')}`)

    // Super admin and org admin can access all locations
    if (user.role === 'super_admin' || user.role === 'org_admin') {
      console.log("🔍 canAccessLocation - Admin user, granting access")
      return true
    }

    // Special handling for receptionists - they should NOT have "all" access
    if (user.role === 'RECEPTIONIST' || user.role === 'receptionist') {
      if (locationId === 'all') {
        console.log("🔍 canAccessLocation - Receptionist cannot access 'all' locations")
        return false
      }
      // Receptionists can only access their specifically assigned location
      const hasAccess = user.locations.includes(locationId)
      console.log(`🔍 canAccessLocation - Receptionist access check: ${hasAccess}`)
      return hasAccess
    }

    // Handle "all" location access for non-receptionist users
    if (locationId === 'all') {
      console.log(`🔍 canAccessLocation - Allowing 'all' access for UI purposes`)
      return true
    }

    // Online location can be accessed by users who have it in their locations
    if (locationId === 'online') {
      const canAccessOnline = user.locations.includes('online') || user.locations.includes('all')
      console.log(`🔍 canAccessLocation - Checking 'online' access: ${canAccessOnline}`)
      return canAccessOnline
    }

    // PRIORITY: Check user's individual location assignments first
    // If the user has "all" in their locations, they can access all locations
    if (user.locations.includes('all')) {
      console.log("🔍 canAccessLocation - User has 'all' in locations, granting access")
      return true
    }

    // For location-restricted users (those without "all" access),
    // prioritize their individual assignments over role-based access
    if (!user.locations.includes('all')) {
      const hasIndividualAccess = user.locations.includes(locationId)
      console.log(`🔍 canAccessLocation - Location-restricted user, individual access check: ${hasIndividualAccess}`)
      console.log(`🔍 canAccessLocation - User's assigned locations: ${user.locations.join(', ')}`)
      return hasIndividualAccess
    }

    // Get the user's role configuration (only for users with "all" access)
    const roles = SettingsStorage.getRoles()
    const userRole = roles.find((role: Role) => role.id === user.role)
    console.log(`🔍 canAccessLocation - User role config:`, userRole?.locationAccess)

    if (userRole?.locationAccess) {
      // Check role-based location access
      if (userRole.locationAccess.type === 'all') {
        console.log("🔍 canAccessLocation - Role has 'all' access, granting access")
        return true
      } else if (userRole.locationAccess.type === 'specific') {
        const hasRoleAccess = userRole.locationAccess.locations.includes(locationId)
        console.log(`🔍 canAccessLocation - Role-based access check: ${hasRoleAccess}`)
        return hasRoleAccess
      }
    }

    // Final fallback - should not reach here for location-restricted users
    console.log("🔍 canAccessLocation - No access granted")
    return false
  }

  // Check if user can access a specific staff member's data
  const canAccessStaffData = (staffId: string): boolean => {
    if (!user) return false

    // Super admin, org admin, and location manager can access all staff data
    if (user.role === 'super_admin' || user.role === 'org_admin' || user.role === 'location_manager') {
      return true
    }

    // Staff can only access their own data
    return user.id === staffId
  }

  // Get user's accessible locations based on role and individual assignments
  const getUserAccessibleLocations = (): string[] => {
    if (!user) return []

    // Super admin and org admin can access all locations
    if (user.role === 'super_admin' || user.role === 'org_admin') {
      return ['all']
    }

    // Special handling for receptionists - they should NEVER have "all" access
    if (user.role === 'RECEPTIONIST' || user.role === 'receptionist') {
      const receptionistLocations = user.locations.filter(loc => loc !== 'all')
      console.log("🔍 getUserAccessibleLocations - Receptionist locations (excluding 'all'):", receptionistLocations)
      return receptionistLocations
    }

    // PRIORITY: Use user's individual location assignments first
    // This ensures location-restricted users are properly limited
    if (user.locations && user.locations.length > 0) {
      console.log("🔍 getUserAccessibleLocations - Using individual user locations:", user.locations)
      return user.locations
    }

    // Get the user's role configuration (only as fallback)
    const roles = SettingsStorage.getRoles()
    const userRole = roles.find((role: Role) => role.id === user.role)

    if (userRole?.locationAccess) {
      // Use role-based location access
      if (userRole.locationAccess.type === 'all') {
        console.log("🔍 getUserAccessibleLocations - Using role-based 'all' access")
        return ['all']
      } else if (userRole.locationAccess.type === 'specific') {
        console.log("🔍 getUserAccessibleLocations - Using role-based specific locations:", userRole.locationAccess.locations)
        return userRole.locationAccess.locations
      }
    }

    // Final fallback - empty array
    console.log("🔍 getUserAccessibleLocations - No accessible locations found")
    return []
  }

  // Check if user can access a specific client's data
  const canAccessClientData = (clientId: string): boolean => {
    if (!user) return false

    // Super admin, org admin, and location manager can access all client data
    if (user.role === 'super_admin' || user.role === 'org_admin' || user.role === 'location_manager') {
      return true
    }

    // Receptionist can access all client data
    if (user.role === 'receptionist') {
      return true
    }

    // Staff can only access clients they have appointments with
    // This would require checking appointments, but for now we'll return false
    // In a real implementation, you would check if the staff has any appointments with this client
    return false
  }

  if (isLoading) {
    return null // Or a loading spinner
  }

  // Create a wrapped setCurrentLocation function that also updates localStorage
  const updateCurrentLocation = (location: string) => {
    // Special handling for receptionists - they cannot access "all" locations
    if (user && (user.role === 'RECEPTIONIST' || user.role === 'receptionist')) {
      if (location === "all") {
        console.log("🚫 Auth Provider - Receptionist cannot access 'all' locations")
        toast({
          title: "Access Denied",
          description: "You can only access your assigned location.",
          variant: "destructive"
        })
        return
      }
      // Receptionists can only access their specifically assigned location
      if (!user.locations.includes(location)) {
        console.log("🚫 Auth Provider - Receptionist cannot change to:", location)
        console.log("🚫 Auth Provider - Receptionist can only access:", user.locations)
        toast({
          title: "Access Denied",
          description: "You can only access your assigned location.",
          variant: "destructive"
        })
        return
      }
    }
    // Check if user is location-restricted (non-receptionist)
    else if (user && !user.locations.includes("all") && user.locations.length > 0) {
      // Location-restricted users can select "all" or their assigned locations
      if (location !== "all" && !user.locations.includes(location)) {
        console.log("🚫 Auth Provider - Location-restricted user cannot change to:", location)
        console.log("🚫 Auth Provider - User can only access:", user.locations)
        toast({
          title: "Access Denied",
          description: "You can only access your assigned location or view all data.",
          variant: "destructive"
        })
        return
      }
    }

    // Save to state
    setCurrentLocation(location)
    // Save to localStorage for persistence
    localStorage.setItem("vanity_location", location)
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        currentLocation,
        setCurrentLocation: updateCurrentLocation,
        login,
        logout,
        hasPermission,
        hasAnyPermission,
        getUserPermissions,
        canAccessLocation,
        canAccessStaffData,
        canAccessClientData,
        getUserAccessibleLocations,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => useContext(AuthContext)

